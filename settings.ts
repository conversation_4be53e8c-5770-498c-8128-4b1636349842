import { createNodeCompoundProgram } from 'sigma/rendering/webgl/programs/common/node';
import NodePointProgram from 'sigma/rendering/webgl/programs/node.point';
import NodePointWithBorderProgram from '@yomguithereal/sigma-experiments-renderers/node/node.point.border';
import NodeHaloProgram from "./node.halo";
import getNodeProgramImage from "./node.image";
import { drawLabel, drawHover } from './node.label';


const startYear = 1939,
  curYear = (new Date).getFullYear(),
  totalYears = curYear - startYear + 1,
  picturesLoadingDelay = 1500,
  playComicsDelay = 1500,
  creatorsRoles = {
    writer: "#234fac",
    artist: "#2b6718",
    both: "#d4a129"
  },
  clusters = {
    creators: {
      "Silver Age": {
        match: ["<PERSON>", "<PERSON>", "<PERSON>"],
        color: "#DDDDDD"
      },
      "Bronze Age": {
        match: ["<PERSON>", "<PERSON>", "<PERSON>"],
        color: "#ff993e"
      },
      "Modern Age": {
        match: ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"],
        color: "#bce25b"
      },
      "<PERSON><PERSON><PERSON> Age": {
        match: ["<PERSON>", "<PERSON> <PERSON> Bendis", "Dan Slott"],
        color: "#5fb1ff"
      }
    },
    characters: {
      // <PERSON> <PERSON> House Colors and Character Groups
      "Gry<PERSON>dor": {
        match: ["<PERSON> <PERSON>", "Hermione Granger", "<PERSON> Weasley", "Ginny Weasley", "<PERSON> Longbottom"],
        color: "#d4af37"  // Gold
      },
      "Slytherin": {
        match: ["Draco Malfoy", "Severus Snape", "Lord Voldemort", "Bellatrix Lestrange", "Lucius Malfoy"],
        color: "#2d5016"  // Dark Green
      },
      "Death Eaters": {
        match: ["Death Eater", "Yaxley", "Wormtail", "Fenrir Greyback"],
        color: "#1a1a1a"  // Dark/Black
      },
      "Order of Phoenix": {
        match: ["Remus Lupin", "Kingsley Shacklebolt", "Mad-Eye Moody", "Tonks"],
        color: "#8b0000"  // Dark Red
      },
      "Ministry Officials": {
        match: ["Pius Thicknesse", "Scrimgeour", "Umbridge"],
        color: "#4b0082"  // Indigo
      },
      "Hogwarts Staff": {
        match: ["Dumbledore", "McGonagall", "Charity Burbage"],
        color: "#800080"  // Purple
      },
      "Magical Creatures": {
        match: ["Nagini", "Griphook", "Dobby"],
        color: "#228b22"  // Forest Green
      },
      "Other Characters": {
        match: ["Mundungus Fletcher", "Xenophilius Lovegood"],
        color: "#cd853f"  // Peru/Brown
      }
    }
  },
  extraPalette = [
    "#d4af37",  // Gryffindor Gold
    "#2d5016",  // Slytherin Green
    "#8b0000",  // Dark Red
    "#4b0082",  // Indigo
    "#800080",  // Purple
    "#228b22",  // Forest Green
    "#cd853f",  // Peru/Brown
    "#1a1a1a",  // Dark/Black
    "#b8860b",  // Dark Goldenrod
    "#556b2f"   // Dark Olive Green
  ],
  smallScreen = Math.min(window.innerWidth, window.innerHeight) < 600,
  NodeProgramImage = getNodeProgramImage(smallScreen ? 96 : 192),
  sigmaSettings = {
    maxCameraRatio: 75,
    defaultEdgeColor: '#ffffff',  // White edges for better visibility on dark background
    defaultEdgeType: 'line',
    edgeLabelFont: '"DejaVu Sans Mono", "DejaVuSansMono", monospace',
    edgeLabelSize: 10,
    edgeLabelColor: {color: '#ffffff'},
    labelRenderer: drawLabel,
    labelFont: '"DejaVu Sans Mono", "DejaVuSansMono", monospace',
    labelColor: {color: '#AAA'},
    labelWeight: 'bold',
    labelDensity: 2, // Higher density to show more labels
    labelGridCellSize: 300,
    hoverRenderer: drawHover,
    zoomToSizeRatioFunction: ratio => Math.pow(ratio, 0.75),
    nodeProgramClasses: {
      circle: createNodeCompoundProgram([
        NodeHaloProgram,
        NodePointProgram
      ]),
      image: createNodeCompoundProgram([
        NodeHaloProgram,
        NodePointWithBorderProgram,
        NodeProgramImage
      ])
    },
    nodeHoverProgramClasses: {
      circle: createNodeCompoundProgram([
        NodePointProgram
      ]),
      image: createNodeCompoundProgram([
        NodePointWithBorderProgram,
        NodeProgramImage
      ])
    }
  };

export {
  startYear, curYear, totalYears,
  picturesLoadingDelay, playComicsDelay,
  creatorsRoles, clusters,
  extraPalette,
  sigmaSettings
};
